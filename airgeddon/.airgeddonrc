#Enabled true / Disabled false - Auto update feature (it has no effect on development mode) - Default value true
AIRGEDDON_AUTO_UPDATE=true

#Enabled true / Disabled false - Skip intro (it has no effect on development mode) - Default value false
AIRGEDDON_SKIP_INTRO=false

#Enabled true / Disabled false - Allow colorized output - Default value true
AIRGEDDON_BASIC_COLORS=true

#Enabled true / Disabled false - Allow extended colorized output (ccze tool needed, it has no effect on disabled basic colors) - Default value true
AIRGEDDON_EXTENDED_COLORS=true

#Enabled true / Disabled false - Auto change language feature - Default value true
AIRGEDDON_AUTO_CHANGE_LANGUAGE=true

#Enabled true / Disabled false - Dependencies, root and bash version checks are done silently (it has no effect on development mode) - Default value false
AIRGEDDON_SILENT_CHECKS=false

#Enabled true / Disabled false - Print help hints on menus - Default value true
AIRGEDDON_PRINT_HINTS=true

#Enabled true / Disabled false - Enable 5Ghz support (it has no effect if your cards are not 5Ghz compatible cards) - Default value true
AIRGEDDON_5GHZ_ENABLED=true

#Enabled true / Disabled false - Force to use iptables instead of nftables (it has no effect if nftables are not present) - Default value false
AIRGEDDON_FORCE_IPTABLES=false

#Enabled true / Disabled false - Force to kill Network Manager before launching Evil Twin attacks - Default value true
AIRGEDDON_FORCE_NETWORK_MANAGER_KILLING=true

#Available values: mdk3, mdk4 - Define which mdk version is going to be used - Default value mdk4
AIRGEDDON_MDK_VERSION=mdk4

#Enabled true / Disabled false - Enable plugins system - Default value true
AIRGEDDON_PLUGINS_ENABLED=true

#Enabled true / Disabled false - Development mode for faster development skipping intro and all initial checks - Default value false
AIRGEDDON_DEVELOPMENT_MODE=false

#Enabled true / Disabled false - Debug mode for development printing debug information - Default value false
AIRGEDDON_DEBUG_MODE=false

#Available values: xterm, tmux - Define the needed tool to be used for windows handling - Default value xterm
AIRGEDDON_WINDOWS_HANDLING=xterm
