name: Compatibility Enhancement
description: Request to expand compatibility support
title: "[Compatibility Enhancement]: "
labels: ["needs triage", "compatibility enhancement"]
projects: ["v1s1t0r1sh3r3/airgeddon"]
body:
  - type: markdown
    attributes:
      value: |
        Please, consider asking your questions in the very active Discord community before opening an issue here. Issues must be opened in English. Submissions that are incomplete or incorrectly filled out will be automatically closed. For more info, check our [Issue Creation Policy](https://github.com/v1s1t0r1sh3r3/airgeddon/blob/master/CONTRIBUTING.md#issue-creation-policy)
  - type: textarea
    id: compatibility
    attributes:
      label: Compatibility Request Description
      description: Describe the compatibility issue or request in as much detail as possible, including hardware, drivers, OS, or environment
      placeholder: Describe what you'd like to see supported...
    validations:
      required: true
  - type: input
    id: affectedtech
    attributes:
      label: Technology or environment
      description: Specify the hardware, chipset, driver, OS, or any other component involved
      placeholder: e.g., PCLinuxOS, BlendOS
    validations:
      required: true
  - type: checkboxes
    id: terms
    attributes:
      label: Acceptance of Issue Creation Policy
      description: By submitting this issue, you agree to follow our [Issue Creation Policy](https://github.com/v1s1t0r1sh3r3/airgeddon/blob/master/CONTRIBUTING.md#issue-creation-policy)
      options:
        - label: I agree to follow this project's Issue Creation policy
          required: true
